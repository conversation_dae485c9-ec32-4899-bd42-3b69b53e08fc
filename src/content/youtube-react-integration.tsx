/**
 * YouTube Integration Module - React版本
 * 使用新的YouTubeButton组件替换原有的DOM操作实现
 */

import React from 'react';
import ReactDOM from 'react-dom';
import { YouTubeButton } from '../components/YouTubeButton';
import { directSubtitleManager } from '../features/subtitle-translation/DirectSubtitleManager';

/**
 * 在YouTube控制栏添加Lucid字幕翻译按钮（React版本）
 */
export async function addYouTubeSubtitleButtonReact(): Promise<void> {
  const maxWaitTime = 10000; // 最多等待10秒
  const checkInterval = 500; // 每500ms检查一次
  const startTime = Date.now();
  
  return new Promise((resolve) => {
    const checkAndAddButton = () => {
      // 查找YouTube控制栏的右侧控制区域
      const rightControls = document.querySelector('.ytp-chrome-bottom .ytp-chrome-controls .ytp-right-controls');
      
      if (rightControls && Date.now() - startTime < maxWaitTime) {
        // 检查按钮是否已存在
        if (document.querySelector('#lucid-youtube-button-container')) {
          console.log('✅ [youtube-button-react|INFO] React字幕翻译按钮已存在');
          resolve();
          return;
        }
        
        try {
          // 创建React容器
          const buttonContainer = document.createElement('div');
          buttonContainer.id = 'lucid-youtube-button-container';
          buttonContainer.style.cssText = `
            margin-right: 8px;
            height: 100%;
            display: flex;
            align-items: center;
          `;
          
          // 将按钮添加到右侧控制栏最前面
          rightControls.insertBefore(buttonContainer, rightControls.firstChild);
          
          // 渲染React组件
          ReactDOM.render(
            <YouTubeButton 
              onSettingsChange={handleYouTubeSubtitleSettingsChange}
            />,
            buttonContainer
          );
          
          console.log('✅ [youtube-button-react|SUCCESS] React字幕翻译按钮已添加到YouTube右侧控制栏');
          resolve();
          
        } catch (error) {
          console.error('❌ [youtube-button-react|ERROR] 创建React按钮失败:', error);
          resolve(); // 即使失败也继续
        }
          
      } else if (Date.now() - startTime >= maxWaitTime) {
        console.warn('⚠️ [youtube-button-react|WARN] YouTube控制栏检测超时，跳过按钮添加');
        resolve();
      } else {
        // 继续检查
        setTimeout(checkAndAddButton, checkInterval);
      }
    };
    
    checkAndAddButton();
  });
}

/**
 * 处理YouTube字幕设置变更
 */
function handleYouTubeSubtitleSettingsChange(settings: any): void {
  console.log('🎯 [youtube-settings|INFO] YouTube字幕设置变更:', settings);

  // 更新Lucid字幕功能状态
  if (typeof settings.lucidSubtitleEnabled !== 'undefined') {
    const isEnabled = settings.lucidSubtitleEnabled;
    console.log(`🔄 [youtube-settings|INFO] ${isEnabled ? '启用' : '禁用'} Lucid字幕功能`);
    
    if (isEnabled) {
      // 启用字幕功能
      document.documentElement.classList.add('lucid-subtitles-enabled');
      directSubtitleManager.enable();
    } else {
      // 禁用字幕功能
      document.documentElement.classList.remove('lucid-subtitles-enabled');
      directSubtitleManager.disable();
    }
  }

  // 更新主字幕语言
  if (settings.primaryLanguage) {
    console.log(`🌍 [youtube-settings|INFO] 切换主字幕语言到: ${settings.primaryLanguage}`);
    directSubtitleManager.setSourceLanguage(settings.primaryLanguage);
  }

  // 更新翻译字幕语言
  if (settings.translationLanguage) {
    console.log(`🔤 [youtube-settings|INFO] 切换翻译语言到: ${settings.translationLanguage}`);
    directSubtitleManager.setTargetLanguage(settings.translationLanguage);
  }

  // 更新翻译引擎
  if (settings.translationEngine) {
    console.log(`🤖 [youtube-settings|INFO] 切换翻译引擎到: ${settings.translationEngine}`);
    directSubtitleManager.setTranslationEngine(settings.translationEngine);
  }

  // 更新字幕显示模式
  if (settings.displayMode) {
    console.log(`📺 [youtube-settings|INFO] 切换字幕显示模式到: ${settings.displayMode}`);
    directSubtitleManager.setDisplayMode(settings.displayMode);
  }

  // 更新字幕样式
  if (settings.subtitleStyle) {
    console.log(`🎨 [youtube-settings|INFO] 更新字幕样式:`, settings.subtitleStyle);
    directSubtitleManager.updateSubtitleStyles(settings.subtitleStyle);
  }

  // 保存设置到存储
  saveYouTubeSubtitleSettings(settings);
}

/**
 * 保存YouTube字幕设置到浏览器存储
 */
async function saveYouTubeSubtitleSettings(settings: any): Promise<void> {
  try {
    const storageKey = 'lucid-youtube-subtitle-settings';
    const existingSettings = await getStoredSettings(storageKey);
    const updatedSettings = { ...existingSettings, ...settings };
    
    await chrome.storage.local.set({ [storageKey]: updatedSettings });
    console.log('✅ [youtube-settings|INFO] 设置已保存到存储');
  } catch (error) {
    console.error('❌ [youtube-settings|ERROR] 保存设置失败:', error);
  }
}

/**
 * 从浏览器存储获取设置
 */
async function getStoredSettings(key: string): Promise<any> {
  try {
    const result = await chrome.storage.local.get(key);
    return result[key] || {};
  } catch (error) {
    console.error('❌ [youtube-settings|ERROR] 获取存储设置失败:', error);
    return {};
  }
}

/**
 * 初始化YouTube页面的React集成
 */
export async function initializeYouTubeReactIntegration(): Promise<void> {
  try {
    console.log('🎬 [youtube-react|STARTUP] 开始初始化YouTube React集成...');
    
    // 等待页面元素就绪
    await waitForVideoPlayer();
    
    // 添加React版本的字幕翻译按钮
    await addYouTubeSubtitleButtonReact();
    
    // 加载保存的设置
    await loadSavedYouTubeSettings();
    
    // 监听键盘快捷键 Ctrl+Shift+S 打开设置
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        directSubtitleManager.toggleSettings();
      }
    });
    
    // 监听页面路由变化（YouTube SPA特性）
    observeYouTubePageChanges();
    
    console.log('✅ [youtube-react|STARTUP] YouTube React集成初始化完成');
    console.log('🔧 [youtube-react|DEBUG] 可用功能:', {
      'React字幕按钮': '已添加到YouTube控制栏',
      'Ctrl+Shift+S': '快捷键打开设置面板',
      '设置持久化': '自动保存和加载用户设置',
      'SPA路由监听': '自动处理YouTube页面切换'
    });
    
  } catch (error) {
    console.error('❌ [youtube-react|ERROR] YouTube React集成初始化失败:', error);
  }
}

/**
 * 加载保存的YouTube字幕设置
 */
async function loadSavedYouTubeSettings(): Promise<void> {
  try {
    const storageKey = 'lucid-youtube-subtitle-settings';
    const savedSettings = await getStoredSettings(storageKey);
    
    if (Object.keys(savedSettings).length > 0) {
      console.log('📄 [youtube-settings|INFO] 加载保存的设置:', savedSettings);
      
      // 应用保存的设置到字幕管理器
      if (savedSettings.lucidSubtitleEnabled !== undefined) {
        savedSettings.lucidSubtitleEnabled ? 
          directSubtitleManager.enable() : 
          directSubtitleManager.disable();
      }
      
      if (savedSettings.sourceLanguage) {
        directSubtitleManager.setSourceLanguage(savedSettings.sourceLanguage);
      }
      
      if (savedSettings.targetLanguage) {
        directSubtitleManager.setTargetLanguage(savedSettings.targetLanguage);
      }
      
      if (savedSettings.translationEngine) {
        directSubtitleManager.setTranslationEngine(savedSettings.translationEngine);
      }
      
      if (savedSettings.displayMode) {
        directSubtitleManager.setDisplayMode(savedSettings.displayMode);
      }
    }
  } catch (error) {
    console.error('❌ [youtube-settings|ERROR] 加载保存设置失败:', error);
  }
}

/**
 * 监听YouTube页面路由变化
 */
function observeYouTubePageChanges(): void {
  let currentUrl = window.location.href;
  
  // 监听YouTube的自定义导航事件
  document.addEventListener('yt-navigate-finish', () => {
    if (window.location.href !== currentUrl) {
      currentUrl = window.location.href;
      
      // 如果导航到视频页面，重新添加按钮
      if (currentUrl.includes('/watch?v=')) {
        console.log('🔄 [youtube-react|INFO] 检测到YouTube页面切换，重新添加按钮');
        setTimeout(() => {
          addYouTubeSubtitleButtonReact();
        }, 1000);
      }
    }
  });
  
  // 备用方案：使用MutationObserver监听DOM变化
  new MutationObserver(() => {
    if (window.location.href !== currentUrl) {
      currentUrl = window.location.href;
      
      if (currentUrl.includes('/watch?v=')) {
        setTimeout(() => {
          addYouTubeSubtitleButtonReact();
        }, 1000);
      }
    }
  }).observe(document, { subtree: true, childList: true });
  
  console.log('👀 [youtube-react|INFO] YouTube页面变化监听器已启动');
}

/**
 * 等待YouTube视频播放器加载
 */
async function waitForVideoPlayer(): Promise<void> {
  const maxWaitTime = 10000; // 最多等待10秒
  const checkInterval = 500; // 每500ms检查一次
  const startTime = Date.now();
  
  return new Promise((resolve) => {
    const checkPlayer = () => {
      const videoPlayer = document.querySelector('#movie_player');
      const videoElement = document.querySelector('video');
      
      if (videoPlayer || videoElement || Date.now() - startTime > maxWaitTime) {
        if (videoPlayer || videoElement) {
          console.log('✅ [youtube-react|INFO] 视频播放器已就绪');
        } else {
          console.warn('⚠️ [youtube-react|WARN] 视频播放器检测超时，继续初始化');
        }
        resolve();
      } else {
        setTimeout(checkPlayer, checkInterval);
      }
    };
    
    checkPlayer();
  });
}

/**
 * 清理YouTube React集成
 */
export function cleanupYouTubeReactIntegration(): void {
  const buttonContainer = document.querySelector('#lucid-youtube-button-container');
  if (buttonContainer) {
    ReactDOM.unmountComponentAtNode(buttonContainer);
    buttonContainer.remove();
    console.log('🧹 [youtube-react|INFO] YouTube React集成已清理');
  }
}