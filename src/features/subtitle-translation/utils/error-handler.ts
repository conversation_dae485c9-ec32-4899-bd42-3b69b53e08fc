/**
 * 字幕翻译模块错误处理工具
 * 
 * 提供统一的错误处理、日志记录和错误恢复机制
 * 遵循 Lucid Extension 的错误处理模式
 */

import { 
  SubtitleError, 
  SubtitleErrorType,
  SubtitleManagerStatus,
  SupportedPlatform,
  SubtitleFormat 
} from '../types';

/**
 * 错误处理选项
 */
interface ErrorHandlingOptions {
  /** 是否记录到控制台 */
  logToConsole?: boolean;
  /** 是否发送到扩展错误监控 */
  reportToExtension?: boolean;
  /** 错误恢复策略 */
  recoveryStrategy?: 'ignore' | 'retry' | 'fallback' | 'reset';
  /** 重试次数（当策略为 retry 时） */
  retryCount?: number;
  /** 上下文信息 */
  context?: Record<string, any>;
}

/**
 * 错误统计信息
 */
interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<SubtitleErrorType, number>;
  errorsByPlatform: Record<SupportedPlatform, number>;
  recentErrors: Array<{
    error: SubtitleError;
    timestamp: number;
    recovered: boolean;
  }>;
  lastErrorTime: number;
}

/**
 * 字幕错误处理器
 */
export class SubtitleErrorHandler {
  private static readonly MAX_RECENT_ERRORS = 50;
  private static readonly ERROR_LOG_PREFIX = '🚨 [SubtitleErrorHandler]';
  
  private static stats: ErrorStats = {
    totalErrors: 0,
    errorsByType: {} as Record<SubtitleErrorType, number>,
    errorsByPlatform: {} as Record<SupportedPlatform, number>,
    recentErrors: [],
    lastErrorTime: 0
  };

  /**
   * 处理字幕相关错误
   */
  static handleError(
    error: Error | SubtitleError, 
    context?: Record<string, any>,
    options: ErrorHandlingOptions = {}
  ): SubtitleError {
    const subtitleError = this.normalizeError(error, context);
    const finalOptions = this.getDefaultOptions(options);
    
    // 更新统计信息
    this.updateStats(subtitleError);
    
    // 记录错误
    if (finalOptions.logToConsole) {
      this.logError(subtitleError, context);
    }
    
    // 上报错误（如果需要）
    if (finalOptions.reportToExtension) {
      this.reportError(subtitleError, context);
    }
    
    // 执行恢复策略
    this.executeRecoveryStrategy(subtitleError, finalOptions);
    
    return subtitleError;
  }

  /**
   * 标准化错误对象
   */
  private static normalizeError(
    error: Error | SubtitleError, 
    context?: Record<string, any>
  ): SubtitleError {
    if (error instanceof SubtitleError) {
      // 添加额外上下文
      if (context) {
        error.context = { ...error.context, ...context };
      }
      return error;
    }
    
    // 转换普通 Error 为 SubtitleError
    return new SubtitleError(
      SubtitleErrorType.UNKNOWN_ERROR,
      error.message || '未知错误',
      error,
      context
    );
  }

  /**
   * 获取默认选项
   */
  private static getDefaultOptions(options: ErrorHandlingOptions): Required<ErrorHandlingOptions> {
    return {
      logToConsole: true,
      reportToExtension: false,
      recoveryStrategy: 'ignore',
      retryCount: 0,
      context: {},
      ...options
    };
  }

  /**
   * 更新错误统计
   */
  private static updateStats(error: SubtitleError): void {
    this.stats.totalErrors++;
    this.stats.lastErrorTime = Date.now();
    
    // 按类型统计
    this.stats.errorsByType[error.type] = (this.stats.errorsByType[error.type] || 0) + 1;
    
    // 按平台统计
    if (error.context?.platform) {
      const platform = error.context.platform as SupportedPlatform;
      this.stats.errorsByPlatform[platform] = (this.stats.errorsByPlatform[platform] || 0) + 1;
    }
    
    // 添加到最近错误列表
    this.stats.recentErrors.unshift({
      error,
      timestamp: Date.now(),
      recovered: false
    });
    
    // 保持最近错误列表大小
    if (this.stats.recentErrors.length > this.MAX_RECENT_ERRORS) {
      this.stats.recentErrors = this.stats.recentErrors.slice(0, this.MAX_RECENT_ERRORS);
    }
  }

  /**
   * 记录错误到控制台
   */
  private static logError(error: SubtitleError, context?: Record<string, any>): void {
    const logLevel = this.getLogLevel(error.type);
    const prefix = `${this.ERROR_LOG_PREFIX} [${error.type}]`;
    
    const logData = {
      message: error.message,
      type: error.type,
      context: { ...error.context, ...context },
      stack: error.originalError?.stack || error.stack,
      timestamp: new Date().toISOString()
    };
    
    switch (logLevel) {
      case 'error':
        console.error(prefix, logData);
        break;
      case 'warn':
        console.warn(prefix, logData);
        break;
      case 'info':
        console.info(prefix, logData);
        break;
      default:
        console.log(prefix, logData);
    }
  }

  /**
   * 获取错误日志级别
   */
  private static getLogLevel(errorType: SubtitleErrorType): 'error' | 'warn' | 'info' | 'debug' {
    switch (errorType) {
      case SubtitleErrorType.NETWORK_INTERCEPTION_FAILED:
      case SubtitleErrorType.CONFIG_ERROR:
        return 'error';
      
      case SubtitleErrorType.PARSING_ERROR:
      case SubtitleErrorType.TRANSLATION_ERROR:
        return 'warn';
      
      case SubtitleErrorType.NETWORK_TIMEOUT:
      case SubtitleErrorType.RATE_LIMIT_EXCEEDED:
        return 'info';
      
      default:
        return 'debug';
    }
  }

  /**
   * 上报错误到扩展监控系统
   */
  private static reportError(error: SubtitleError, context?: Record<string, any>): void {
    try {
      // 这里可以集成到扩展的错误监控系统
      // 例如发送到 background script 或外部监控服务
      if (typeof browser !== 'undefined' && browser.runtime?.sendMessage) {
        browser.runtime.sendMessage({
          type: 'subtitle-error-report',
          error: {
            type: error.type,
            message: error.message,
            context: { ...error.context, ...context },
            timestamp: Date.now(),
            stack: error.originalError?.stack
          }
        }).catch(err => {
          console.warn('⚠️ [SubtitleErrorHandler] 错误上报失败:', err);
        });
      }
    } catch (reportError) {
      console.warn('⚠️ [SubtitleErrorHandler] 错误上报异常:', reportError);
    }
  }

  /**
   * 执行错误恢复策略
   */
  private static executeRecoveryStrategy(
    error: SubtitleError, 
    options: Required<ErrorHandlingOptions>
  ): void {
    switch (options.recoveryStrategy) {
      case 'retry':
        this.scheduleRetry(error, options);
        break;
      
      case 'fallback':
        this.executeFallback(error, options);
        break;
      
      case 'reset':
        this.executeReset(error, options);
        break;
      
      case 'ignore':
      default:
        // 什么都不做，只记录
        break;
    }
  }

  /**
   * 安排重试
   */
  private static scheduleRetry(error: SubtitleError, options: Required<ErrorHandlingOptions>): void {
    if (options.retryCount > 0) {
      console.log(`🔄 [SubtitleErrorHandler] 安排重试: ${error.type}, ${options.retryCount} 次`);
      
      // 这里可以实现具体的重试逻辑
      // 例如通过事件系统通知相关组件重试
      this.emit('retry-requested', {
        error,
        retryCount: options.retryCount,
        context: options.context
      });
    }
  }

  /**
   * 执行回退策略
   */
  private static executeFallback(error: SubtitleError, options: Required<ErrorHandlingOptions>): void {
    console.log(`🔀 [SubtitleErrorHandler] 执行回退策略: ${error.type}`);
    
    // 根据错误类型执行不同的回退策略
    switch (error.type) {
      case SubtitleErrorType.PARSING_ERROR:
        this.emit('fallback-parser-requested', { error, context: options.context });
        break;
      
      case SubtitleErrorType.NETWORK_INTERCEPTION_FAILED:
        this.emit('fallback-interception-requested', { error, context: options.context });
        break;
      
      default:
        this.emit('generic-fallback-requested', { error, context: options.context });
    }
  }

  /**
   * 执行重置策略
   */
  private static executeReset(error: SubtitleError, options: Required<ErrorHandlingOptions>): void {
    console.log(`🔄 [SubtitleErrorHandler] 执行重置策略: ${error.type}`);
    
    this.emit('system-reset-requested', {
      error,
      context: options.context,
      resetType: this.getResetType(error.type)
    });
  }

  /**
   * 获取重置类型
   */
  private static getResetType(errorType: SubtitleErrorType): 'soft' | 'hard' {
    switch (errorType) {
      case SubtitleErrorType.CONFIG_ERROR:
      case SubtitleErrorType.NETWORK_INTERCEPTION_FAILED:
        return 'hard';
      
      default:
        return 'soft';
    }
  }

  /**
   * 简单事件发射器
   */
  private static eventListeners = new Map<string, Set<(data: any) => void>>();

  private static emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`❌ [SubtitleErrorHandler] 事件监听器错误 (${event}):`, error);
        }
      });
    }
  }

  static on(event: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  static off(event: string, listener: (data: any) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * 获取错误统计信息
   */
  static getStats(): ErrorStats {
    return { ...this.stats };
  }

  /**
   * 清空错误统计
   */
  static clearStats(): void {
    this.stats = {
      totalErrors: 0,
      errorsByType: {} as Record<SubtitleErrorType, number>,
      errorsByPlatform: {} as Record<SupportedPlatform, number>,
      recentErrors: [],
      lastErrorTime: 0
    };
  }

  /**
   * 检查是否为关键错误（需要立即处理）
   */
  static isCriticalError(error: SubtitleError): boolean {
    const criticalTypes = [
      SubtitleErrorType.CONFIG_ERROR,
      SubtitleErrorType.NETWORK_INTERCEPTION_FAILED
    ];
    
    return criticalTypes.includes(error.type);
  }

  /**
   * 检查错误频率是否异常
   */
  static isErrorRateHigh(timeWindowMs: number = 60000): boolean {
    const cutoffTime = Date.now() - timeWindowMs;
    const recentErrorCount = this.stats.recentErrors.filter(
      item => item.timestamp > cutoffTime
    ).length;
    
    // 如果在时间窗口内错误超过 10 个，认为频率异常
    return recentErrorCount > 10;
  }

  /**
   * 获取建议的恢复策略
   */
  static getRecommendedRecoveryStrategy(error: SubtitleError): 'ignore' | 'retry' | 'fallback' | 'reset' {
    // 基于错误类型和历史统计推荐策略
    const errorCount = this.stats.errorsByType[error.type] || 0;
    
    if (this.isCriticalError(error)) {
      return 'reset';
    }
    
    if (errorCount < 3) {
      return 'retry';
    }
    
    if (errorCount < 10) {
      return 'fallback';
    }
    
    return 'ignore';
  }

  /**
   * 创建便捷的错误处理方法
   */
  static createErrorHandler(
    defaultOptions: Partial<ErrorHandlingOptions> = {}
  ): (error: Error | SubtitleError, context?: Record<string, any>) => SubtitleError {
    return (error: Error | SubtitleError, context?: Record<string, any>) => {
      return this.handleError(error, context, defaultOptions);
    };
  }

  /**
   * 包装异步函数以自动处理错误
   */
  static wrapAsync<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    options: ErrorHandlingOptions = {}
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        const handledError = this.handleError(error, { functionName: fn.name }, options);
        throw handledError;
      }
    };
  }

  /**
   * 包装同步函数以自动处理错误
   */
  static wrapSync<T extends any[], R>(
    fn: (...args: T) => R,
    options: ErrorHandlingOptions = {}
  ): (...args: T) => R {
    return (...args: T): R => {
      try {
        return fn(...args);
      } catch (error) {
        const handledError = this.handleError(error, { functionName: fn.name }, options);
        throw handledError;
      }
    };
  }
}

/**
 * 便捷的错误处理装饰器函数
 */
export function handleSubtitleErrors(options: ErrorHandlingOptions = {}) {
  return function <T extends any[], R>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R> | R>
  ) {
    const originalMethod = descriptor.value;
    if (!originalMethod) return descriptor;

    descriptor.value = function (...args: T): Promise<R> | R {
      try {
        const result = originalMethod.apply(this, args);
        
        if (result instanceof Promise) {
          return result.catch(error => {
            const handledError = SubtitleErrorHandler.handleError(
              error, 
              { className: target.constructor.name, methodName: propertyKey },
              options
            );
            throw handledError;
          });
        }
        
        return result;
      } catch (error) {
        const handledError = SubtitleErrorHandler.handleError(
          error,
          { className: target.constructor.name, methodName: propertyKey },
          options
        );
        throw handledError;
      }
    } as any;

    return descriptor;
  };
}

/**
 * 预定义的错误处理器实例
 */
export const subtitleErrorHandler = SubtitleErrorHandler.createErrorHandler({
  logToConsole: true,
  reportToExtension: false,
  recoveryStrategy: 'retry',
  retryCount: 2
});

export const criticalErrorHandler = SubtitleErrorHandler.createErrorHandler({
  logToConsole: true,
  reportToExtension: true,
  recoveryStrategy: 'reset',
  retryCount: 0
});

export const silentErrorHandler = SubtitleErrorHandler.createErrorHandler({
  logToConsole: false,
  reportToExtension: false,
  recoveryStrategy: 'ignore',
  retryCount: 0
});