/**
 * 时间解析标准规范
 * 定义严格的时间格式解析规则，消除妥协性设计
 */

/**
 * 时间解析标准接口
 */
export interface TimeParsingStandard {
  readonly name: string;
  readonly pattern: RegExp;
  readonly description: string;
  readonly example: string;
}

/**
 * 支持的时间格式标准
 */
export const TIME_FORMAT_STANDARDS: readonly TimeParsingStandard[] = [
  {
    name: 'SRT_STANDARD',
    pattern: /^(\d{2}):(\d{2}):(\d{2}),(\d{3})$/,
    description: 'SRT标准格式：HH:MM:SS,mmm（逗号分隔，3位毫秒）',
    example: '00:01:23,456'
  },
  {
    name: 'SRT_FLEXIBLE',
    pattern: /^(\d{2}):(\d{2}):(\d{2}),(\d{1,3})$/,
    description: 'SRT灵活格式：HH:MM:SS,m[mm]（逗号分隔，1-3位毫秒，用于兼容）',
    example: '00:01:23,1 或 00:01:23,456'
  },
  {
    name: 'VTT_STANDARD', 
    pattern: /^(\d{2}):(\d{2}):(\d{2})\.(\d{3})$/,
    description: 'VTT标准格式：HH:MM:SS.mmm（点分隔，3位毫秒）',
    example: '00:01:23.456'
  },
  {
    name: 'FLEXIBLE_HOURS',
    pattern: /^(\d{1,2}):(\d{2}):(\d{2})[,.](\d{1,3})$/,
    description: '灵活小时位数：H:MM:SS.mmm 或 HH:MM:SS.mmm',
    example: '1:23:45.678 或 01:23:45.678'
  },
  {
    name: 'SECONDS_DECIMAL',
    pattern: /^(\d+(?:\.\d{1,3})?)$/,
    description: '纯秒数格式（支持小数）',
    example: '123.456'
  }
] as const;

/**
 * 时间解析错误类型
 */
export enum TimeParsingError {
  INVALID_FORMAT = 'INVALID_FORMAT',
  OUT_OF_RANGE = 'OUT_OF_RANGE',  
  MILLISECOND_PRECISION = 'MILLISECOND_PRECISION'
}

/**
 * 时间解析异常类
 */
export class TimeParsingException extends Error {
  constructor(
    public readonly errorType: TimeParsingError,
    public readonly timeString: string,
    message: string
  ) {
    super(`时间解析失败 [${errorType}]: ${message} (输入: "${timeString}")`);
    this.name = 'TimeParsingException';
  }
}

/**
 * 标准化时间解析器
 * 提供严格的时间格式解析，不做妥协性转换
 */
export class StandardTimeParser {
  
  /**
   * 解析时间字符串为毫秒
   * @param timeStr 时间字符串
   * @param allowedFormats 允许的格式（默认所有）
   * @returns 毫秒数
   * @throws TimeParsingException 格式不匹配时抛出异常
   */
  static parseToMs(
    timeStr: string, 
    allowedFormats: readonly TimeParsingStandard[] = TIME_FORMAT_STANDARDS
  ): number {
    if (!timeStr || typeof timeStr !== 'string') {
      throw new TimeParsingException(
        TimeParsingError.INVALID_FORMAT,
        String(timeStr),
        '输入不是有效字符串'
      );
    }

    const trimmed = timeStr.trim();
    
    // 尝试每种支持的格式
    for (const format of allowedFormats) {
      const match = trimmed.match(format.pattern);
      
      if (match) {
        return this.parseMatchedTime(match, format, trimmed);
      }
    }

    // 没有匹配的格式，抛出详细错误
    const supportedFormats = allowedFormats.map(f => f.example).join(', ');
    throw new TimeParsingException(
      TimeParsingError.INVALID_FORMAT,
      trimmed,
      `不支持的时间格式。支持的格式: ${supportedFormats}`
    );
  }

  /**
   * 解析匹配的时间组件
   */
  private static parseMatchedTime(
    match: RegExpMatchArray,
    format: TimeParsingStandard,
    original: string
  ): number {
    try {
      // 处理纯秒数格式
      if (format.name === 'SECONDS_DECIMAL') {
        const seconds = parseFloat(match[1]);
        this.validateTimeRange(0, 0, seconds, 0, original);
        return Math.round(seconds * 1000);
      }

      // 处理 HH:MM:SS 格式
      const hours = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseInt(match[3], 10);
      const milliseconds = parseInt(match[4], 10);

      // 严格验证时间范围
      this.validateTimeRange(hours, minutes, seconds, milliseconds, original);

      // 验证毫秒精度 - 对于SRT_FLEXIBLE格式，标准化毫秒位数
      let normalizedMs = milliseconds;
      if (format.name === 'SRT_FLEXIBLE' && match[4].length < 3) {
        // 标准化毫秒位数：1 -> 100, 12 -> 120, 123 -> 123
        normalizedMs = match[4].padEnd(3, '0');
      } else if (match[4].length !== 3 && format.name !== 'SRT_FLEXIBLE') {
        throw new TimeParsingException(
          TimeParsingError.MILLISECOND_PRECISION,
          original,
          `毫秒位数必须为3位，实际为 ${match[4].length} 位: "${match[4]}"`
        );
      }

      return (
        hours * 3600000 +
        minutes * 60000 +
        seconds * 1000 +
        parseInt(normalizedMs)
      );

    } catch (error) {
      if (error instanceof TimeParsingException) {
        throw error;
      }
      
      throw new TimeParsingException(
        TimeParsingError.INVALID_FORMAT,
        original,
        `解析时间组件失败: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 验证时间范围的合法性
   */
  private static validateTimeRange(
    hours: number,
    minutes: number, 
    seconds: number,
    milliseconds: number,
    original: string
  ): void {
    if (hours < 0 || hours > 23) {
      throw new TimeParsingException(
        TimeParsingError.OUT_OF_RANGE,
        original,
        `小时数超出范围 [0-23]: ${hours}`
      );
    }

    if (minutes < 0 || minutes > 59) {
      throw new TimeParsingException(
        TimeParsingError.OUT_OF_RANGE,
        original,
        `分钟数超出范围 [0-59]: ${minutes}`
      );
    }

    if (seconds < 0 || seconds > 59) {
      throw new TimeParsingException(
        TimeParsingError.OUT_OF_RANGE,
        original,
        `秒数超出范围 [0-59]: ${seconds}`
      );
    }

    if (milliseconds < 0 || milliseconds > 999) {
      throw new TimeParsingException(
        TimeParsingError.OUT_OF_RANGE,
        original,
        `毫秒数超出范围 [0-999]: ${milliseconds}`
      );
    }
  }

  /**
   * 毫秒转标准时间字符串
   * @param ms 毫秒数
   * @param format 输出格式（默认SRT格式）
   * @returns 格式化的时间字符串
   */
  static msToTimeString(
    ms: number,
    format: 'SRT' | 'VTT' = 'SRT'
  ): string {
    if (ms < 0) {
      throw new Error(`时间不能为负数: ${ms}ms`);
    }

    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    const milliseconds = ms % 1000;

    const separator = format === 'SRT' ? ',' : '.';
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}${separator}${milliseconds.toString().padStart(3, '0')}`;
  }

  /**
   * 获取特定格式的解析器
   */
  static getFormatParser(formatName: string) {
    const format = TIME_FORMAT_STANDARDS.find(f => f.name === formatName);
    if (!format) {
      throw new Error(`未知的时间格式: ${formatName}`);
    }
    
    return (timeStr: string) => this.parseToMs(timeStr, [format]);
  }
}