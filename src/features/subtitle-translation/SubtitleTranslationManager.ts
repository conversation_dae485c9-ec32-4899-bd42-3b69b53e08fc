import React from 'react';
import { SubtitleOverlay } from '@components/SubtitleOverlay';
import { SettingsPanel, SettingItem } from '@components/SettingsPanel';
import { ShadowView } from '@ui-manager/ShadowView';
import { uiManager } from '@ui-manager';
import { SUBTITLE_OVERLAY_CSS } from '@ui-manager/subtitle-styles';
import { SETTINGS_PANEL_CSS } from '@ui-manager/settings-panel-styles';

export interface SubtitleTranslationConfig {
  enabled: boolean;
  primaryLanguage: string;
  translationLanguage: string;
  translationEngine: string;
  displayMode: 'bilingual' | 'original' | 'translation';
  autoTranslate: boolean;
}

export class SubtitleTranslationManager {
  private config: SubtitleTranslationConfig;
  private subtitleView: ShadowView | null = null;
  private settingsView: ShadowView | null = null;
  private currentSubtitle: { original: string; translated: string } | null = null;
  private isSettingsVisible = false;

  constructor() {
    this.config = {
      enabled: true,
      primaryLanguage: 'English',
      translationLanguage: '中文',
      translationEngine: 'Microsoft',
      displayMode: 'bilingual',
      autoTranslate: true
    };

    this.setupEventListeners();
  }

  private setupEventListeners() {
    // 监听键盘快捷键
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        this.toggleSettings();
      }
    });

    // 监听页面点击，关闭设置面板
    document.addEventListener('click', (event) => {
      if (this.isSettingsVisible && this.settingsView) {
        const settingsElement = this.settingsView.getContainer();
        if (settingsElement && !settingsElement.contains(event.target as Node)) {
          this.hideSettings();
        }
      }
    });
  }

  public showSubtitle(originalText: string, translatedText: string, position?: { x: number; y: number }) {
    if (!this.config.enabled) return;

    this.currentSubtitle = { original: originalText, translated: translatedText };

    // 根据显示模式决定显示内容
    let displayOriginal = '';
    let displayTranslated = '';

    switch (this.config.displayMode) {
      case 'bilingual':
        displayOriginal = originalText;
        displayTranslated = translatedText;
        break;
      case 'original':
        displayOriginal = originalText;
        break;
      case 'translation':
        displayTranslated = translatedText;
        break;
    }

    if (this.subtitleView) {
      this.subtitleView.destroy();
    }

    this.subtitleView = uiManager.createShadowView({
      component: SubtitleOverlay,
      props: {
        originalText: displayOriginal,
        translatedText: displayTranslated,
        visible: true,
        position
      },
      containerId: 'lucid-subtitle-overlay',
      styles: SUBTITLE_OVERLAY_CSS,
      position: position || { x: 0, y: 0 }
    });
  }

  public hideSubtitle() {
    if (this.subtitleView) {
      this.subtitleView.destroy();
      this.subtitleView = null;
    }
    this.currentSubtitle = null;
  }

  public toggleSettings() {
    if (this.isSettingsVisible) {
      this.hideSettings();
    } else {
      this.showSettings();
    }
  }

  private showSettings() {
    const settingItems = this.createSettingItems();
    
    if (this.settingsView) {
      this.settingsView.destroy();
    }

    // 计算设置面板位置（右上角）
    const position = {
      x: window.innerWidth - 300,
      y: 50
    };

    this.settingsView = uiManager.createShadowView({
      component: SettingsPanel,
      props: {
        items: settingItems,
        visible: true,
        position,
        onClose: () => this.hideSettings()
      },
      containerId: 'lucid-settings-panel',
      styles: SETTINGS_PANEL_CSS,
      position
    });

    this.isSettingsVisible = true;
  }

  private hideSettings() {
    if (this.settingsView) {
      this.settingsView.destroy();
      this.settingsView = null;
    }
    this.isSettingsVisible = false;
  }

  private createSettingItems(): SettingItem[] {
    return [
      {
        id: 'subtitle-enabled',
        icon: this.createSubtitleIcon(),
        label: 'Lucid 字幕',
        type: 'toggle',
        enabled: this.config.enabled,
        onChange: (enabled: boolean) => {
          this.config.enabled = enabled;
          this.saveConfig();
        }
      },
      {
        id: 'primary-language',
        icon: this.createLanguageIcon(),
        label: '主字幕',
        type: 'select',
        value: this.config.primaryLanguage,
        options: [
          { label: 'English', value: 'English' },
          { label: '中文', value: '中文' },
          { label: '日本語', value: '日本語' }
        ]
      },
      {
        id: 'translation-language',
        icon: this.createTranslationIcon(),
        label: '翻译字幕',
        type: 'select',
        value: this.config.translationLanguage,
        options: [
          { label: '中文', value: '中文' },
          { label: 'English', value: 'English' },
          { label: '日本語', value: '日本語' }
        ]
      },
      {
        id: 'translation-engine',
        icon: this.createEngineIcon(),
        label: '翻译引擎',
        type: 'select',
        value: this.config.translationEngine,
        options: [
          { label: 'Microsoft', value: 'Microsoft' },
          { label: 'Google', value: 'Google' },
          { label: 'DeepL', value: 'DeepL' }
        ]
      },
      {
        id: 'display-mode',
        icon: this.createDisplayIcon(),
        label: '字幕显示',
        type: 'select',
        value: this.getDisplayModeLabel(),
        options: [
          { label: '双语字幕', value: 'bilingual' },
          { label: '仅原文', value: 'original' },
          { label: '仅译文', value: 'translation' }
        ]
      },
      {
        id: 'subtitle-style',
        icon: this.createStyleIcon(),
        label: '字幕样式',
        type: 'action',
        onClick: () => {
          // TODO: 打开样式设置
          console.log('Open subtitle style settings');
        }
      },
      {
        id: 'keyboard-shortcuts',
        icon: this.createKeyboardIcon(),
        label: '设置快捷键',
        type: 'action',
        onClick: () => {
          // TODO: 打开快捷键设置
          console.log('Open keyboard shortcuts settings');
        }
      }
    ];
  }

  private getDisplayModeLabel(): string {
    switch (this.config.displayMode) {
      case 'bilingual': return '双语字幕';
      case 'original': return '仅原文';
      case 'translation': return '仅译文';
      default: return '双语字幕';
    }
  }

  private saveConfig() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.set({ 'lucid-subtitle-config': this.config });
      } else {
        localStorage.setItem('lucid-subtitle-config', JSON.stringify(this.config));
      }
    } catch (error) {
      console.error('Failed to save subtitle config:', error);
    }
  }

  private async loadConfig() {
    try {
      let savedConfig;
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get('lucid-subtitle-config');
        savedConfig = result['lucid-subtitle-config'];
      } else {
        const saved = localStorage.getItem('lucid-subtitle-config');
        savedConfig = saved ? JSON.parse(saved) : null;
      }
      
      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      console.error('Failed to load subtitle config:', error);
    }
  }

  // Icon creation methods
  private createSubtitleIcon() {
    return React.createElement('svg', {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, React.createElement('path', {
      d: "M4.16667 16.6666C3.70833 16.6666 3.31597 16.5034 2.98958 16.177C2.66319 15.8506 2.5 15.4583 2.5 14.9999V4.99992C2.5 4.54159 2.66319 4.14922 2.98958 3.82284C3.31597 3.49645 3.70833 3.33325 4.16667 3.33325H15.8333C16.2917 3.33325 16.684 3.49645 17.0104 3.82284C17.3368 4.14922 17.5 4.54159 17.5 4.99992V14.9999C17.5 15.4583 17.3368 15.8506 17.0104 16.177C16.684 16.5034 16.2917 16.6666 15.8333 16.6666H4.16667ZM4.16667 14.9999H15.8333V4.99992H4.16667V14.9999ZM5.83333 12.4999H8.33333C8.56944 12.4999 8.76736 12.4201 8.92708 12.2603C9.08681 12.1006 9.16667 11.9027 9.16667 11.6666V11.2499C9.16667 11.1249 9.125 11.0208 9.04167 10.9374C8.95833 10.8541 8.85417 10.8124 8.72917 10.8124H8.35417C8.22917 10.8124 8.125 10.8541 8.04167 10.9374C7.95833 11.0208 7.91667 11.1249 7.91667 11.2499H6.25V8.74992H7.91667C7.91667 8.87492 7.95833 8.97909 8.04167 9.06242C8.125 9.14575 8.22917 9.18742 8.35417 9.18742H8.72917C8.85417 9.18742 8.95833 9.14575 9.04167 9.06242C9.125 8.97909 9.16667 8.87492 9.16667 8.74992V8.33325C9.16667 8.09714 9.08681 7.89922 8.92708 7.7395C8.76736 7.57978 8.56944 7.49992 8.33333 7.49992H5.83333C5.59722 7.49992 5.39931 7.57978 5.23958 7.7395C5.07986 7.89922 5 8.09714 5 8.33325V11.6666C5 11.9027 5.07986 12.1006 5.23958 12.2603C5.39931 12.4201 5.59722 12.4999 5.83333 12.4999ZM14.1667 7.49992H11.6667C11.4306 7.49992 11.2326 7.57978 11.0729 7.7395C10.9132 7.89922 10.8333 8.09714 10.8333 8.33325V11.6666C10.8333 11.9027 10.9132 12.1006 11.0729 12.2603C11.2326 12.4201 11.4306 12.4999 11.6667 12.4999H14.1667C14.4028 12.4999 14.6007 12.4201 14.7604 12.2603C14.9201 12.1006 15 11.9027 15 11.6666V11.2499C15 11.1249 14.9583 11.0208 14.875 10.9374C14.7917 10.8541 14.6875 10.8124 14.5625 10.8124H14.1875C14.0625 10.8124 13.9583 10.8541 13.875 10.9374C13.7917 11.0208 13.75 11.1249 13.75 11.2499H12.0833V8.74992H13.75C13.75 8.87492 13.7917 8.97909 13.875 9.06242C13.9583 9.14575 14.0625 9.18742 14.1875 9.18742H14.5625C14.6875 9.18742 14.7917 9.14575 14.875 9.06242C14.9583 8.97909 15 8.87492 15 8.74992V8.33325C15 8.09714 14.9201 7.89922 14.7604 7.7395C14.6007 7.57978 14.4028 7.49992 14.1667 7.49992Z",
      fill: "white",
      fillOpacity: "0.8"
    }));
  }

  private createLanguageIcon() {
    return React.createElement('svg', {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, React.createElement('path', {
      d: "M10.0001 18.3334C8.86119 18.3334 7.7848 18.1147 6.77092 17.6772C5.75703 17.2397 4.87161 16.6424 4.11467 15.8855C3.35772 15.1286 2.7605 14.2431 2.323 13.2292C1.8855 12.2154 1.66675 11.139 1.66675 10.0001C1.66675 8.8473 1.8855 7.76744 2.323 6.7605C2.7605 5.75355 3.35772 4.87161 4.11467 4.11467C4.87161 3.35772 5.75703 2.7605 6.77092 2.323C7.7848 1.8855 8.86119 1.66675 10.0001 1.66675C11.1529 1.66675 12.2327 1.8855 13.2397 2.323C14.2466 2.7605 15.1286 3.35772 15.8855 4.11467C16.6424 4.87161 17.2397 5.75355 17.6772 6.7605C18.1147 7.76744 18.3334 8.8473 18.3334 10.0001C18.3334 11.139 18.1147 12.2154 17.6772 13.2292C17.2397 14.2431 16.6424 15.1286 15.8855 15.8855C15.1286 16.6424 14.2466 17.2397 13.2397 17.6772C12.2327 18.1147 11.1529 18.3334 10.0001 18.3334ZM10.0001 16.6251C10.3612 16.1251 10.6737 15.6042 10.9376 15.0626C11.2015 14.5209 11.4167 13.9445 11.5834 13.3334H8.41675C8.58342 13.9445 8.79869 14.5209 9.06258 15.0626C9.32647 15.6042 9.63897 16.1251 10.0001 16.6251ZM7.83342 16.2917C7.58342 15.8334 7.36467 15.3577 7.17717 14.8647C6.98967 14.3716 6.83342 13.8612 6.70842 13.3334H4.25008C4.65286 14.0279 5.15633 14.632 5.7605 15.1459C6.36467 15.6598 7.05564 16.0417 7.83342 16.2917ZM12.1667 16.2917C12.9445 16.0417 13.6355 15.6598 14.2397 15.1459C14.8438 14.632 15.3473 14.0279 15.7501 13.3334H13.2917C13.1667 13.8612 13.0105 14.3716 12.823 14.8647C12.6355 15.3577 12.4167 15.8334 12.1667 16.2917ZM3.54175 11.6667H6.37508C6.33342 11.389 6.30217 11.1147 6.28133 10.8438C6.2605 10.573 6.25008 10.2917 6.25008 10.0001C6.25008 9.70842 6.2605 9.42717 6.28133 9.15633C6.30217 8.8855 6.33342 8.61119 6.37508 8.33342H3.54175C3.4723 8.61119 3.42022 8.8855 3.3855 9.15633C3.35078 9.42717 3.33341 9.70842 3.33341 10.0001C3.33341 10.2917 3.35078 10.573 3.3855 10.8438C3.42022 11.1147 3.4723 11.389 3.54175 11.6667ZM8.04175 11.6667H11.9584C12.0001 11.389 12.0313 11.1147 12.0522 10.8438C12.073 10.573 12.0834 10.2917 12.0834 10.0001C12.0834 9.70842 12.073 9.42717 12.0522 9.15633C12.0313 8.8855 12.0001 8.61119 11.9584 8.33342H8.04175C8.00008 8.61119 7.96883 8.8855 7.948 9.15633C7.92717 9.42717 7.91675 9.70842 7.91675 10.0001C7.91675 10.2917 7.92717 10.573 7.948 10.8438C7.96883 11.1147 8.00008 11.389 8.04175 11.6667ZM13.6251 11.6667H16.4584C16.5279 11.389 16.5799 11.1147 16.6147 10.8438C16.6494 10.573 16.6667 10.2917 16.6667 10.0001C16.6667 9.70842 16.6494 9.42717 16.6147 9.15633C16.5799 8.8855 16.5279 8.61119 16.4584 8.33342H13.6251C13.6667 8.61119 13.698 8.8855 13.7188 9.15633C13.7397 9.42717 13.7501 9.70842 13.7501 10.0001C13.7501 10.2917 13.7397 10.573 13.7188 10.8438C13.698 11.1147 13.6667 11.389 13.6251 11.6667ZM13.2917 6.66675H15.7501C15.3473 5.9723 14.8438 5.36814 14.2397 4.85425C13.6355 4.34036 12.9445 3.95841 12.1667 3.70841C12.4167 4.16675 12.6355 4.64244 12.823 5.1355C13.0105 5.62855 13.1667 6.13897 13.2917 6.66675ZM8.41675 6.66675H11.5834C11.4167 6.05564 11.2015 5.47925 10.9376 4.93758C10.6737 4.39592 10.3612 3.87508 10.0001 3.37508C9.63897 3.87508 9.32647 4.39592 9.06258 4.93758C8.79869 5.47925 8.58342 6.05564 8.41675 6.66675ZM4.25008 6.66675H6.70842C6.83342 6.13897 6.98967 5.62855 7.17717 5.1355C7.36467 4.64244 7.58342 4.16675 7.83342 3.70841C7.05564 3.95841 6.36467 4.34036 5.7605 4.85425C5.15633 5.36814 4.65286 5.9723 4.25008 6.66675Z",
      fill: "white",
      fillOpacity: "0.8"
    }));
  }

  private createTranslationIcon() {
    return React.createElement('svg', {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, React.createElement('path', {
      d: "M16.4279 6.77334L16.2224 7.24474C16.0721 7.58985 15.5947 7.58985 15.4443 7.24474L15.2389 6.77334C14.8727 5.93281 14.213 5.26359 13.3898 4.89749L12.7567 4.61594C12.4145 4.46369 12.4145 3.96559 12.7567 3.81335L13.3544 3.54754C14.1987 3.17201 14.8702 2.47803 15.2302 1.60894L15.4412 1.09953C15.5882 0.744493 16.0786 0.744493 16.2256 1.09953L16.4366 1.60894C16.7966 2.47803 17.4681 3.17201 18.3124 3.54754L18.91 3.81335C19.2523 3.96559 19.2523 4.46369 18.91 4.61594L18.277 4.89749C17.4538 5.26359 16.7942 5.93281 16.4279 6.77334ZM4.16675 14.1666V12.4999H2.50008V14.1666C2.50008 16.0075 3.99246 17.4999 5.83341 17.4999H8.33341V15.8333H5.83341L5.70903 15.8287C4.84665 15.7651 4.16675 15.0453 4.16675 14.1666ZM18.6667 17.4999L15.0001 8.33325H13.3334L9.66758 17.4999H11.4626L12.4617 14.9999H15.8701L16.8709 17.4999H18.6667ZM13.1276 13.3333L14.1667 10.7376L15.2042 13.3333H13.1276ZM6.66675 3.33325V1.66659H5.00008V3.33325H1.66675V9.16659H5.00008V11.6666H6.66675V9.16659H10.0001V3.33325H6.66675ZM3.33341 4.99992H5.00008V7.49992H3.33341V4.99992ZM6.66675 4.99992H8.33341V7.49992H6.66675V4.99992Z",
      fill: "white",
      fillOpacity: "0.8"
    }));
  }

  private createEngineIcon() {
    return React.createElement('svg', {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, React.createElement('path', {
      d: "M17.0557 7.24474L17.2612 6.77334C17.6275 5.93281 18.2872 5.26359 19.1103 4.89749L19.7433 4.61594C20.0857 4.46369 20.0857 3.96559 19.7433 3.81335L19.1457 3.54754C18.3014 3.17201 17.6299 2.47803 17.2699 1.60894L17.0589 1.09953C16.9119 0.744493 16.4216 0.744493 16.2745 1.09953L16.0635 1.60894C15.7036 2.47803 15.0321 3.17201 14.1877 3.54754L13.5901 3.81335C13.2478 3.96559 13.2478 4.46369 13.5901 4.61594L14.2232 4.89749C15.0463 5.26359 15.706 5.93281 16.0722 6.77334L16.2777 7.24474C16.4281 7.58985 16.9054 7.58985 17.0557 7.24474ZM4.83336 13.3333H6.62841L7.12841 12.0833H9.53833L10.0383 13.3333H11.8333L9.16666 6.66659H7.50002L4.83336 13.3333ZM8.33336 9.07092L8.87166 10.4166H7.79507L8.33336 9.07092ZM12.5001 13.3333V6.66659H14.1667V13.3333H12.5001ZM2.50008 2.49992C2.03985 2.49992 1.66675 2.87302 1.66675 3.33325V16.6666C1.66675 17.1268 2.03985 17.4999 2.50008 17.4999H17.5001C17.9603 17.4999 18.3334 17.1268 18.3334 16.6666V9.16659H16.6667V15.8333H3.33341V4.16659H11.6667V2.49992H2.50008Z",
      fill: "white",
      fillOpacity: "0.8"
    }));
  }

  private createDisplayIcon() {
    return React.createElement('svg', {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, React.createElement('path', {
      d: "M5.00008 13.3333H11.6667V11.6666H5.00008V13.3333ZM13.3334 13.3333H15.0001V11.6666H13.3334V13.3333ZM5.00008 9.99992H6.66675V8.33325H5.00008V9.99992ZM8.33342 9.99992H15.0001V8.33325H8.33342V9.99992ZM3.33341 16.6666C2.87508 16.6666 2.48272 16.5034 2.15633 16.177C1.82994 15.8506 1.66675 15.4583 1.66675 14.9999V4.99992C1.66675 4.54159 1.82994 4.14922 2.15633 3.82284C2.48272 3.49645 2.87508 3.33325 3.33341 3.33325H16.6667C17.1251 3.33325 17.5174 3.49645 17.8438 3.82284C18.1702 4.14922 18.3334 4.54159 18.3334 4.99992V14.9999C18.3334 15.4583 18.1702 15.8506 17.8438 16.177C17.5174 16.5034 17.1251 16.6666 16.6667 16.6666H3.33341ZM3.33341 14.9999H16.6667V4.99992H3.33341V14.9999Z",
      fill: "white",
      fillOpacity: "0.8"
    }));
  }

  private createStyleIcon() {
    return React.createElement('svg', {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, React.createElement('path', {
      d: "M6.66675 9.99992V8.33325H5.00008V9.99992H6.66675ZM8.91675 9.99992C9.06953 9.68047 9.24661 9.38186 9.448 9.10409C9.64939 8.82631 9.87508 8.56936 10.1251 8.33325H8.33342V9.99992H8.91675ZM8.41675 13.3333C8.38897 13.1944 8.37161 13.0589 8.36467 12.927C8.35772 12.7951 8.35425 12.6527 8.35425 12.4999C8.35425 12.3471 8.35772 12.2048 8.36467 12.0728C8.37161 11.9409 8.38897 11.8055 8.41675 11.6666H5.00008V13.3333H8.41675ZM10.1042 16.6666H3.33341C2.87508 16.6666 2.48272 16.5034 2.15633 16.177C1.82994 15.8506 1.66675 15.4583 1.66675 14.9999V4.99992C1.66675 4.54159 1.82994 4.14922 2.15633 3.82284C2.48272 3.49645 2.87508 3.33325 3.33341 3.33325H16.6667C17.1251 3.33325 17.5174 3.49645 17.8438 3.82284C18.1702 4.14922 18.3334 4.54159 18.3334 4.99992V8.45825C18.0973 8.20825 17.8404 7.98256 17.5626 7.78117C17.2848 7.57978 16.9862 7.4027 16.6667 7.24992V4.99992H3.33341V14.9999H8.91675C9.06953 15.3194 9.24314 15.618 9.43758 15.8958C9.63203 16.1735 9.85425 16.4305 10.1042 16.6666ZM15.0001 16.6666H13.3334L13.0834 15.4166C12.9167 15.3471 12.7605 15.2742 12.6147 15.1978C12.4688 15.1214 12.3195 15.0277 12.1667 14.9166L10.9584 15.2916L10.1251 13.8749L11.0834 13.0416C11.0556 12.861 11.0417 12.6805 11.0417 12.4999C11.0417 12.3194 11.0556 12.1388 11.0834 11.9583L10.1251 11.1249L10.9584 9.70825L12.1667 10.0833C12.3195 9.97214 12.4688 9.87839 12.6147 9.802C12.7605 9.72561 12.9167 9.6527 13.0834 9.58325L13.3334 8.33325H15.0001L15.2501 9.58325C15.4167 9.6527 15.5765 9.73256 15.7292 9.82284C15.882 9.91311 16.0279 10.0138 16.1667 10.1249L17.3751 9.70825L18.2084 11.1666L17.2501 11.9999C17.2779 12.1805 17.2917 12.3541 17.2917 12.5208C17.2917 12.6874 17.2779 12.861 17.2501 13.0416L18.2084 13.8749L17.3751 15.2916L16.1667 14.9166C16.014 15.0277 15.8647 15.1214 15.7188 15.1978C15.573 15.2742 15.4167 15.3471 15.2501 15.4166L15.0001 16.6666ZM14.1667 14.1666C14.6251 14.1666 15.0174 14.0034 15.3438 13.677C15.6702 13.3506 15.8334 12.9583 15.8334 12.4999C15.8334 12.0416 15.6702 11.6492 15.3438 11.3228C15.0174 10.9964 14.6251 10.8333 14.1667 10.8333C13.7084 10.8333 13.3161 10.9964 12.9897 11.3228C12.6633 11.6492 12.5001 12.0416 12.5001 12.4999C12.5001 12.9583 12.6633 13.3506 12.9897 13.677C13.3161 14.0034 13.7084 14.1666 14.1667 14.1666Z",
      fill: "white",
      fillOpacity: "0.8"
    }));
  }

  private createKeyboardIcon() {
    return React.createElement('svg', {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, React.createElement('path', {
      d: "M3.66659 4.45833V15.5417H16.3333V4.45833H3.66659ZM2.87492 2.875H17.1249C17.5622 2.875 17.9166 3.22945 17.9166 3.66667V16.3333C17.9166 16.7706 17.5622 17.125 17.1249 17.125H2.87492C2.4377 17.125 2.08325 16.7706 2.08325 16.3333V3.66667C2.08325 3.22945 2.4377 2.875 2.87492 2.875ZM5.24992 6.04167H6.83325V7.625H5.24992V6.04167ZM5.24992 9.20833H6.83325V10.7917H5.24992V9.20833ZM5.24992 12.375H14.7499V13.9583H5.24992V12.375ZM9.20825 9.20833H10.7916V10.7917H9.20825V9.20833ZM9.20825 6.04167H10.7916V7.625H9.20825V6.04167ZM13.1666 6.04167H14.7499V7.625H13.1666V6.04167ZM13.1666 9.20833H14.7499V10.7917H13.1666V9.20833Z",
      fill: "white",
      fillOpacity: "0.8"
    }));
  }

  public getConfig(): SubtitleTranslationConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<SubtitleTranslationConfig>) {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
  }

  public destroy() {
    this.hideSubtitle();
    this.hideSettings();
    document.removeEventListener('keydown', this.setupEventListeners);
  }
}

// 创建全局实例
export const subtitleTranslationManager = new SubtitleTranslationManager();